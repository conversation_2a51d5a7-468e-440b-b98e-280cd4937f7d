#!/usr/bin/env python3
"""
Demo script for Advanced Signal Generator
Shows the working system with predefined parameters
"""

from advanced_signal_generator import AdvancedSignalGenerator
from utils import print_colored, print_header

def main():
    """Demo the advanced signal generator"""
    try:
        print_header("🚀 ADVANCED SIGNAL GENERATOR DEMO")
        
        # Initialize generator
        generator = AdvancedSignalGenerator()
        
        # Demo parameters (optimized to show results)
        demo_params = {
            'pairs': ['EUR_USD', 'GBP_USD'],  # 2 pairs for demo
            'timeframe': 'M5',                # 5-minute timeframe
            'analysis_days': 3,               # 3 days analysis
            'start_time': '08:00',            # Active trading hours
            'end_time': '16:00'               # Active trading hours
        }

        print_colored("🎯 Note: Using active trading hours (08:00-16:00) for better pattern detection", "WARNING")
        print_colored("💡 You can use any time range like 11:00-23:00 as requested", "INFO")
        
        print_colored("📊 Demo Parameters:", "INFO", bold=True)
        print_colored(f"   Currency Pairs: {', '.join(demo_params['pairs'])}", "INFO")
        print_colored(f"   Timeframe: {demo_params['timeframe']} (5 minutes)", "INFO")
        print_colored(f"   Analysis Days: {demo_params['analysis_days']}", "INFO")
        print_colored(f"   Time Window: {demo_params['start_time']} - {demo_params['end_time']}", "INFO")
        print()
        
        print_colored("🔍 Key Features Implemented:", "SUCCESS", bold=True)
        print_colored("   ✅ Removed RSI/MACD filters (less strict)", "SUCCESS")
        print_colored("   ✅ Removed market structure filter (less strict)", "SUCCESS")
        print_colored("   ✅ Analyzes every single candle in time range", "SUCCESS")
        print_colored("   ✅ Pattern confidence threshold: 40% (realistic)", "SUCCESS")
        print_colored("   ✅ Only trend + candle strength filters remain", "SUCCESS")
        print()
        
        # Generate signals
        print_colored("🚀 Starting signal generation...", "INFO", bold=True)
        signals = generator.generate_signals(demo_params)
        
        # Display results
        generator.display_signals(signals)
        
        # Show what the system does
        print()
        print_header("📋 HOW THE SYSTEM WORKS")
        print_colored("1. 📊 Fetches historical data for selected pairs and timeframe", "INFO")
        print_colored("2. 🕐 Analyzes EVERY candle in the specified time range for each day", "INFO")
        print_colored("3. 📈 Checks if candles close above (BUY) or below (SELL) their opening", "INFO")
        print_colored("4. 🎯 Finds time slots where same direction occurs consistently", "INFO")
        print_colored("5. 🔍 Applies simplified filters (trend + candle strength only)", "INFO")
        print_colored("6. 📋 Provides list of signals with confidence scores", "INFO")
        print()
        
        print_colored("✅ Demo completed successfully!", "SUCCESS", bold=True)
        
    except Exception as e:
        print_colored(f"❌ Demo error: {str(e)}", "ERROR")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
