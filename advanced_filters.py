#!/usr/bin/env python3
"""
Advanced Filters for Signal Enhancement
Applies trend confirmation, market structure, and technical indicator filters
"""

import pandas as pd
import numpy as np
from datetime import datetime
from utils import add_technical_indicators, print_colored
from config import ADVANCED_SIGNAL_CONFIG

class AdvancedFilters:
    def __init__(self):
        """Initialize the advanced filters"""
        self.config = ADVANCED_SIGNAL_CONFIG
        self.scoring_weights = self.config['SCORING_WEIGHTS']
        self.filters_config = self.config['FILTERS']
    
    def filter_signals(self, patterns, historical_data, pair):
        """Apply all advanced filters to the patterns"""
        if not patterns:
            return []
        
        print_colored(f"🔍 Applying advanced filters to {len(patterns)} patterns...", "INFO")
        
        # Add technical indicators to historical data
        df_with_indicators = add_technical_indicators(historical_data.copy())
        
        filtered_signals = []
        
        for pattern in patterns:
            # Apply filters and calculate scores
            filter_results = self.apply_all_filters(pattern, df_with_indicators, pair)
            
            # Calculate final confidence score
            final_signal = self.calculate_final_score(pattern, filter_results)
            
            if final_signal['confidence_score'] >= 30:  # Lower threshold - less strict
                filtered_signals.append(final_signal)
        
        print_colored(f"✅ {len(filtered_signals)} signals passed filters", "SUCCESS")
        return filtered_signals
    
    def apply_all_filters(self, pattern, df, pair):
        """Apply all configured filters to a pattern"""
        filter_results = {
            'trend_filter': 0,
            'strength_filter': 0,
            'filters_passed': 0,
            'filter_details': {}
        }

        # Get the most recent data for current market conditions
        recent_data = df.tail(50)  # Last 50 candles for context

        if self.filters_config['USE_TREND_FILTER']:
            filter_results['trend_filter'] = self.apply_trend_filter(pattern, recent_data)
            filter_results['filter_details']['trend'] = self.get_trend_analysis(recent_data)

        if self.filters_config['USE_STRENGTH_FILTER']:
            filter_results['strength_filter'] = self.apply_strength_filter(pattern)
            filter_results['filter_details']['strength'] = self.get_strength_analysis(pattern)

        # Count passed filters (only trend and strength now)
        filter_results['filters_passed'] = sum([
            1 if filter_results['trend_filter'] > 0 else 0,
            1 if filter_results['strength_filter'] > 0 else 0
        ])

        return filter_results
    
    def apply_trend_filter(self, pattern, df):
        """Apply trend confirmation filter"""
        if len(df) < 20:
            return 0
        
        signal = pattern['signal']
        
        # Calculate trend using multiple moving averages
        sma_20 = df['sma_20'].iloc[-1]
        ema_12 = df['ema_12'].iloc[-1]
        ema_26 = df['ema_26'].iloc[-1]
        current_price = df['close'].iloc[-1]
        
        # Determine trend direction
        trend_score = 0
        
        if signal == 'BUY':
            # For BUY signals, prefer uptrend
            if current_price > sma_20 and ema_12 > ema_26:
                trend_score = self.scoring_weights['TREND_CONFIRMATION']
            elif current_price > sma_20 or ema_12 > ema_26:
                trend_score = self.scoring_weights['TREND_CONFIRMATION'] * 0.5
        
        elif signal == 'SELL':
            # For SELL signals, prefer downtrend
            if current_price < sma_20 and ema_12 < ema_26:
                trend_score = self.scoring_weights['TREND_CONFIRMATION']
            elif current_price < sma_20 or ema_12 < ema_26:
                trend_score = self.scoring_weights['TREND_CONFIRMATION'] * 0.5
        
        return trend_score
    
    def apply_structure_filter(self, pattern, df):
        """Apply market structure filter (support/resistance)"""
        if len(df) < 20:
            return 0
        
        signal = pattern['signal']
        current_price = df['close'].iloc[-1]
        
        # Calculate support and resistance levels
        recent_highs = df['high'].rolling(window=10).max()
        recent_lows = df['low'].rolling(window=10).min()
        
        resistance_level = recent_highs.iloc[-5:].max()
        support_level = recent_lows.iloc[-5:].min()
        
        structure_score = 0
        
        # Check if signal aligns with structure
        price_range = resistance_level - support_level
        if price_range <= 0:
            return 0
        
        if signal == 'BUY':
            # BUY signals better near support
            distance_from_support = (current_price - support_level) / price_range
            if distance_from_support <= 0.3:  # Within 30% of support
                structure_score = self.scoring_weights['MARKET_STRUCTURE']
            elif distance_from_support <= 0.5:
                structure_score = self.scoring_weights['MARKET_STRUCTURE'] * 0.5
        
        elif signal == 'SELL':
            # SELL signals better near resistance
            distance_from_resistance = (resistance_level - current_price) / price_range
            if distance_from_resistance <= 0.3:  # Within 30% of resistance
                structure_score = self.scoring_weights['MARKET_STRUCTURE']
            elif distance_from_resistance <= 0.5:
                structure_score = self.scoring_weights['MARKET_STRUCTURE'] * 0.5
        
        return structure_score
    
    def apply_rsi_filter(self, pattern, df):
        """Apply RSI overbought/oversold filter"""
        if 'rsi' not in df.columns or df['rsi'].isna().all():
            return 0
        
        signal = pattern['signal']
        current_rsi = df['rsi'].iloc[-1]
        
        if pd.isna(current_rsi):
            return 0
        
        rsi_score = 0
        
        if signal == 'BUY':
            # BUY signals better when RSI is oversold or neutral
            if current_rsi <= 30:  # Oversold
                rsi_score = self.scoring_weights['RSI_CONFIRMATION']
            elif current_rsi <= 50:  # Neutral to oversold
                rsi_score = self.scoring_weights['RSI_CONFIRMATION'] * 0.5
        
        elif signal == 'SELL':
            # SELL signals better when RSI is overbought or neutral
            if current_rsi >= 70:  # Overbought
                rsi_score = self.scoring_weights['RSI_CONFIRMATION']
            elif current_rsi >= 50:  # Neutral to overbought
                rsi_score = self.scoring_weights['RSI_CONFIRMATION'] * 0.5
        
        return rsi_score
    
    def apply_macd_filter(self, pattern, df):
        """Apply MACD momentum filter"""
        if 'macd' not in df.columns or 'macd_signal' not in df.columns:
            return 0
        
        if df['macd'].isna().all() or df['macd_signal'].isna().all():
            return 0
        
        signal = pattern['signal']
        current_macd = df['macd'].iloc[-1]
        current_signal = df['macd_signal'].iloc[-1]
        
        if pd.isna(current_macd) or pd.isna(current_signal):
            return 0
        
        macd_score = 0
        
        if signal == 'BUY':
            # BUY signals better when MACD is bullish
            if current_macd > current_signal and current_macd > 0:
                macd_score = self.scoring_weights['MACD_CONFIRMATION']
            elif current_macd > current_signal:
                macd_score = self.scoring_weights['MACD_CONFIRMATION'] * 0.5
        
        elif signal == 'SELL':
            # SELL signals better when MACD is bearish
            if current_macd < current_signal and current_macd < 0:
                macd_score = self.scoring_weights['MACD_CONFIRMATION']
            elif current_macd < current_signal:
                macd_score = self.scoring_weights['MACD_CONFIRMATION'] * 0.5
        
        return macd_score
    
    def apply_strength_filter(self, pattern):
        """Apply candle strength filter"""
        strength_score = 0
        
        # Base score for pattern consistency
        if pattern['strong_ratio'] >= 0.7:  # 70% strong candles
            strength_score = self.scoring_weights['CANDLE_STRENGTH']
        elif pattern['strong_ratio'] >= 0.5:  # 50% strong candles
            strength_score = self.scoring_weights['CANDLE_STRENGTH'] * 0.5
        
        return strength_score
    
    def calculate_final_score(self, pattern, filter_results):
        """Calculate final confidence score combining pattern and filters"""
        # Base pattern score
        base_score = pattern['confidence'] * self.scoring_weights['PATTERN_CONSISTENCY']

        # Add filter scores (only trend and strength now)
        total_filter_score = (
            filter_results['trend_filter'] +
            filter_results['strength_filter']
        )

        # Calculate final score
        total_score = base_score + total_filter_score
        confidence_score = min(total_score, 100)  # Cap at 100%

        # Create final signal object
        final_signal = {
            'time': pattern['time'],
            'signal': pattern['signal'],
            'pattern_type': pattern['pattern_type'],
            'confidence_score': confidence_score,
            'total_score': total_score,
            'base_pattern_score': base_score,
            'filter_score': total_filter_score,
            'filters_passed': filter_results['filters_passed'],
            'pattern_confidence': pattern['confidence'],
            'pattern_occurrences': f"{pattern['signal_occurrences']}/{pattern['total_occurrences']}",
            'filter_details': filter_results['filter_details'],
            'raw_pattern': pattern
        }

        return final_signal
    
    def get_trend_analysis(self, df):
        """Get trend analysis details"""
        if len(df) < 20:
            return "Insufficient data"
        
        sma_20 = df['sma_20'].iloc[-1]
        current_price = df['close'].iloc[-1]
        ema_12 = df['ema_12'].iloc[-1]
        ema_26 = df['ema_26'].iloc[-1]
        
        if current_price > sma_20 and ema_12 > ema_26:
            return "Strong Uptrend"
        elif current_price > sma_20 or ema_12 > ema_26:
            return "Weak Uptrend"
        elif current_price < sma_20 and ema_12 < ema_26:
            return "Strong Downtrend"
        elif current_price < sma_20 or ema_12 < ema_26:
            return "Weak Downtrend"
        else:
            return "Sideways"
    
    def get_structure_analysis(self, df):
        """Get market structure analysis"""
        if len(df) < 20:
            return "Insufficient data"
        
        current_price = df['close'].iloc[-1]
        recent_high = df['high'].tail(10).max()
        recent_low = df['low'].tail(10).min()
        
        range_size = recent_high - recent_low
        if range_size <= 0:
            return "No clear structure"
        
        position = (current_price - recent_low) / range_size
        
        if position >= 0.7:
            return "Near Resistance"
        elif position <= 0.3:
            return "Near Support"
        else:
            return "Mid-range"
    
    def get_rsi_analysis(self, df):
        """Get RSI analysis"""
        if 'rsi' not in df.columns or df['rsi'].isna().all():
            return "RSI not available"
        
        current_rsi = df['rsi'].iloc[-1]
        if pd.isna(current_rsi):
            return "RSI not available"
        
        if current_rsi >= 70:
            return f"Overbought ({current_rsi:.1f})"
        elif current_rsi <= 30:
            return f"Oversold ({current_rsi:.1f})"
        else:
            return f"Neutral ({current_rsi:.1f})"
    
    def get_macd_analysis(self, df):
        """Get MACD analysis"""
        if 'macd' not in df.columns or 'macd_signal' not in df.columns:
            return "MACD not available"
        
        if df['macd'].isna().all() or df['macd_signal'].isna().all():
            return "MACD not available"
        
        current_macd = df['macd'].iloc[-1]
        current_signal = df['macd_signal'].iloc[-1]
        
        if pd.isna(current_macd) or pd.isna(current_signal):
            return "MACD not available"
        
        if current_macd > current_signal:
            return "Bullish momentum"
        else:
            return "Bearish momentum"
    
    def get_strength_analysis(self, pattern):
        """Get candle strength analysis"""
        strong_ratio = pattern['strong_ratio']
        avg_body_ratio = pattern['avg_body_ratio']
        
        return f"Strong: {strong_ratio:.1%}, Avg Body: {avg_body_ratio:.1%}"
