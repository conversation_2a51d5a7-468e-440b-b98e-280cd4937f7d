#!/usr/bin/env python3
"""
Test script for Advanced Signal Generator
"""

import pandas as pd
from datetime import datetime
from utils import fetch_historical_candles, print_colored, print_header
from pattern_analyzer import Pat<PERSON>Analyzer
from advanced_filters import AdvancedFilters

def test_data_fetching():
    """Test data fetching functionality"""
    print_header("🔍 TESTING DATA FETCHING")
    
    pair = "EUR_USD"
    timeframe = "M5"
    days = 3
    
    # Calculate candles needed
    candles_per_day = {
        'M1': 1440,   # 24 * 60
        'M5': 288,    # 24 * 12
        'M15': 96,    # 24 * 4
        'M30': 48,    # 24 * 2
        'H1': 24      # 24 * 1
    }
    
    total_candles = candles_per_day.get(timeframe, 288) * days
    print_colored(f"📊 Fetching {total_candles} candles for {pair} ({timeframe})", "INFO")
    
    # Fetch data
    df = fetch_historical_candles(pair, total_candles, timeframe)
    
    if df is not None:
        print_colored(f"✅ Fetched {len(df)} candles", "SUCCESS")
        print_colored(f"📅 Date range: {df['time'].iloc[0]} to {df['time'].iloc[-1]}", "INFO")
        
        # Show sample data
        print_colored("📊 Sample data:", "INFO")
        print(df.head())
        print()
        print(df.tail())
        
        return df
    else:
        print_colored("❌ Failed to fetch data", "ERROR")
        return None

def test_pattern_analysis():
    """Test pattern analysis functionality"""
    print_header("🔍 TESTING PATTERN ANALYSIS")

    # Fetch test data
    df = test_data_fetching()
    if df is None:
        return

    # Show time distribution
    print_colored("📊 Analyzing time distribution in data:", "INFO")
    df['datetime'] = pd.to_datetime(df['time'])
    df['hour'] = df['datetime'].dt.hour
    df['date'] = df['datetime'].dt.date

    # Show hours with data
    hour_counts = df['hour'].value_counts().sort_index()
    print_colored("🕐 Hours with data:", "INFO")
    for hour, count in hour_counts.items():
        print_colored(f"  {hour:02d}:xx - {count} candles", "INFO")

    # Initialize pattern analyzer
    analyzer = PatternAnalyzer()

    # Test with active trading hours (London/NY overlap)
    analysis_days = 3
    start_time = "08:00"  # More active time
    end_time = "16:00"    # More active time
    timeframe = "M5"

    print_colored(f"🔍 Analyzing patterns for {analysis_days} days", "INFO")
    print_colored(f"🕐 Time window: {start_time} - {end_time} (active trading hours)", "INFO")

    # Find patterns
    patterns = analyzer.find_time_patterns(
        df, analysis_days, start_time, end_time, timeframe
    )

    print_colored(f"📊 Found {len(patterns)} patterns", "SUCCESS")

    for i, pattern in enumerate(patterns[:5]):  # Show first 5 patterns
        print_colored(f"\nPattern {i+1}:", "HEADER")
        print_colored(f"  Time: {pattern['time']}", "INFO")
        print_colored(f"  Signal: {pattern['signal']}", "BUY" if pattern['signal'] == 'BUY' else "SELL")
        print_colored(f"  Confidence: {pattern['confidence']:.2%}", "SUCCESS")
        print_colored(f"  Occurrences: {pattern['signal_occurrences']}/{pattern['total_occurrences']}", "INFO")

    return patterns

def test_filters():
    """Test advanced filters functionality"""
    print_header("🔍 TESTING ADVANCED FILTERS")
    
    # Get patterns from previous test
    patterns = test_pattern_analysis()
    if not patterns:
        print_colored("❌ No patterns to filter", "ERROR")
        return
    
    # Fetch data for filters
    df = fetch_historical_candles("EUR_USD", 500, "M5")
    if df is None:
        print_colored("❌ Failed to fetch data for filters", "ERROR")
        return
    
    # Initialize filters
    filters = AdvancedFilters()
    
    # Apply filters
    filtered_signals = filters.filter_signals(patterns, df, "EUR_USD")
    
    print_colored(f"📊 Filtered signals: {len(filtered_signals)}", "SUCCESS")
    
    for i, signal in enumerate(filtered_signals[:3]):  # Show first 3 signals
        print_colored(f"\nFiltered Signal {i+1}:", "HEADER")
        print_colored(f"  Time: {signal['time']}", "INFO")
        print_colored(f"  Signal: {signal['signal']}", "BUY" if signal['signal'] == 'BUY' else "SELL")
        print_colored(f"  Confidence Score: {signal['confidence_score']:.1f}%", "SUCCESS")
        print_colored(f"  Filters Passed: {signal['filters_passed']}/5", "INFO")
        print_colored(f"  Total Score: {signal['total_score']:.1f}", "INFO")

def test_with_lower_threshold():
    """Test with lower confidence threshold"""
    print_header("🔍 TESTING WITH LOWER THRESHOLD")
    
    # Temporarily lower the threshold
    from config import ADVANCED_SIGNAL_CONFIG
    original_threshold = ADVANCED_SIGNAL_CONFIG['MIN_PATTERN_CONFIDENCE']
    ADVANCED_SIGNAL_CONFIG['MIN_PATTERN_CONFIDENCE'] = 0.4  # Lower threshold
    
    print_colored(f"📊 Lowered threshold to {ADVANCED_SIGNAL_CONFIG['MIN_PATTERN_CONFIDENCE']:.1%}", "INFO")
    
    try:
        # Run pattern analysis again
        patterns = test_pattern_analysis()
        
        if patterns:
            print_colored(f"✅ Found {len(patterns)} patterns with lower threshold", "SUCCESS")
        else:
            print_colored("❌ Still no patterns found", "WARNING")
    
    finally:
        # Restore original threshold
        ADVANCED_SIGNAL_CONFIG['MIN_PATTERN_CONFIDENCE'] = original_threshold
        print_colored(f"📊 Restored threshold to {original_threshold:.1%}", "INFO")

def main():
    """Main test function"""
    try:
        print_header("🧪 ADVANCED SIGNAL GENERATOR TESTING")
        
        # Test 1: Data fetching
        test_data_fetching()
        print()
        
        # Test 2: Pattern analysis
        test_pattern_analysis()
        print()
        
        # Test 3: Advanced filters
        test_filters()
        print()
        
        # Test 4: Lower threshold
        test_with_lower_threshold()
        
        print_colored("✅ All tests completed!", "SUCCESS", bold=True)
        
    except Exception as e:
        print_colored(f"❌ Test error: {str(e)}", "ERROR")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
