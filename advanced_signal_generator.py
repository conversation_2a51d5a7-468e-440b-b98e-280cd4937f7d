#!/usr/bin/env python3
"""
Advanced Signal Generator with Historical Pattern Recognition
Analyzes historical patterns at specific times and generates advanced trading signals
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
import time as time_module
from pattern_analyzer import PatternAnalyzer
from advanced_filters import AdvancedFilters
from utils import (
    fetch_historical_candles, print_colored, print_header, 
    select_currency_pairs, format_price, format_percentage
)
from config import CURRENCY_PAIRS, ADVANCED_SIGNAL_CONFIG, DISPLAY_CONFIG

class AdvancedSignalGenerator:
    def __init__(self):
        """Initialize the advanced signal generator"""
        self.pattern_analyzer = PatternAnalyzer()
        self.advanced_filters = AdvancedFilters()
        self.signals = []
        
    def get_user_inputs(self):
        """Get user inputs for signal generation parameters"""
        print_header("🔧 SIGNAL GENERATION SETUP")
        
        # Select currency pairs
        print_colored("📊 Select Currency Pairs:", "INFO", bold=True)
        selected_pairs = select_currency_pairs()
        print()
        
        # Select timeframe
        print_colored("⏰ Select Timeframe:", "INFO", bold=True)
        timeframes = ADVANCED_SIGNAL_CONFIG["TIMEFRAMES"]
        timeframe_names = ADVANCED_SIGNAL_CONFIG["TIMEFRAME_NAMES"]
        
        for i, tf in enumerate(timeframes, 1):
            print_colored(f"{i}. {timeframe_names[tf]} ({tf})", "INFO")
        
        while True:
            try:
                tf_choice = int(input("\nEnter timeframe choice (1-5): ").strip())
                if 1 <= tf_choice <= len(timeframes):
                    selected_timeframe = timeframes[tf_choice - 1]
                    break
                else:
                    print_colored("❌ Invalid choice. Please enter 1-5.", "ERROR")
            except ValueError:
                print_colored("❌ Please enter a valid number.", "ERROR")
        
        print_colored(f"✅ Selected: {timeframe_names[selected_timeframe]}", "SUCCESS")
        print()
        
        # Number of days to analyze
        print_colored("📅 Analysis Period:", "INFO", bold=True)
        default_days = ADVANCED_SIGNAL_CONFIG["DEFAULT_ANALYSIS_DAYS"]
        min_days = ADVANCED_SIGNAL_CONFIG["MIN_ANALYSIS_DAYS"]
        max_days = ADVANCED_SIGNAL_CONFIG["MAX_ANALYSIS_DAYS"]
        
        print_colored(f"Enter number of days to analyze ({min_days}-{max_days}, default: {default_days}):", "INFO")
        
        while True:
            try:
                days_input = input("Days: ").strip()
                if not days_input:
                    analysis_days = default_days
                    break
                
                analysis_days = int(days_input)
                if min_days <= analysis_days <= max_days:
                    break
                else:
                    print_colored(f"❌ Please enter a number between {min_days} and {max_days}.", "ERROR")
            except ValueError:
                print_colored("❌ Please enter a valid number.", "ERROR")
        
        print_colored(f"✅ Analyzing {analysis_days} days of historical data", "SUCCESS")
        print()
        
        # Time window
        print_colored("🕐 Analysis Time Window:", "INFO", bold=True)
        default_start = ADVANCED_SIGNAL_CONFIG["DEFAULT_START_TIME"]
        default_end = ADVANCED_SIGNAL_CONFIG["DEFAULT_END_TIME"]
        
        print_colored(f"Enter start time (HH:MM format, default: {default_start}):", "INFO")
        start_time_input = input("Start time: ").strip()
        start_time = start_time_input if start_time_input else default_start
        
        print_colored(f"Enter end time (HH:MM format, default: {default_end}):", "INFO")
        end_time_input = input("End time: ").strip()
        end_time = end_time_input if end_time_input else default_end
        
        print_colored(f"✅ Time window: {start_time} - {end_time}", "SUCCESS")
        print()
        
        return {
            'pairs': selected_pairs,
            'timeframe': selected_timeframe,
            'analysis_days': analysis_days,
            'start_time': start_time,
            'end_time': end_time
        }
    
    def generate_signals(self, params):
        """Generate advanced signals based on historical pattern analysis"""
        print_header("🔍 ANALYZING HISTORICAL PATTERNS")
        
        all_signals = []
        
        for pair in params['pairs']:
            print_colored(f"📊 Analyzing {pair}...", "INFO")
            
            try:
                # Fetch historical data
                historical_data = self.fetch_extended_historical_data(
                    pair, params['timeframe'], params['analysis_days'] + 2
                )
                
                if historical_data is None or len(historical_data) < 100:
                    print_colored(f"❌ Insufficient data for {pair}", "ERROR")
                    continue
                
                # Analyze patterns
                patterns = self.pattern_analyzer.find_time_patterns(
                    historical_data, 
                    params['analysis_days'],
                    params['start_time'],
                    params['end_time'],
                    params['timeframe']
                )
                
                # Apply advanced filters
                filtered_signals = self.advanced_filters.filter_signals(
                    patterns, historical_data, pair
                )
                
                # Add to results
                for signal in filtered_signals:
                    signal['pair'] = pair
                    signal['timeframe'] = params['timeframe']
                    all_signals.append(signal)
                    
            except Exception as e:
                print_colored(f"❌ Error analyzing {pair}: {str(e)}", "ERROR")
                continue
        
        return all_signals
    
    def fetch_extended_historical_data(self, pair, timeframe, days):
        """Fetch extended historical data for pattern analysis"""
        try:
            # Calculate the number of candles needed
            candles_per_day = {
                'M1': 1440,   # 24 * 60
                'M5': 288,    # 24 * 12
                'M15': 96,    # 24 * 4
                'M30': 48,    # 24 * 2
                'H1': 24      # 24 * 1
            }
            
            total_candles = candles_per_day.get(timeframe, 1440) * days
            
            # Fetch historical data
            return fetch_historical_candles(pair, total_candles, timeframe)
            
        except Exception as e:
            print_colored(f"❌ Error fetching data for {pair}: {str(e)}", "ERROR")
            return None
    
    def display_signals(self, signals):
        """Display generated signals in a formatted table"""
        if not signals:
            print_colored("❌ No signals found matching the criteria", "WARNING")
            return
        
        print_header(f"🎯 ADVANCED SIGNALS GENERATED ({len(signals)} signals)")
        
        # Sort signals by confidence score (highest first)
        signals.sort(key=lambda x: x.get('confidence_score', 0), reverse=True)
        
        # Print table header
        self.print_signal_table_header()
        
        # Print signals
        for signal in signals:
            self.print_signal_row(signal)
        
        print()
        print_colored(f"📊 Total signals: {len(signals)}", "INFO", bold=True)
        
        # Summary statistics
        buy_signals = [s for s in signals if s['signal'] == 'BUY']
        sell_signals = [s for s in signals if s['signal'] == 'SELL']
        
        print_colored(f"📈 BUY signals: {len(buy_signals)}", "BUY")
        print_colored(f"📉 SELL signals: {len(sell_signals)}", "SELL")
        
        if signals:
            avg_confidence = sum(s.get('confidence_score', 0) for s in signals) / len(signals)
            print_colored(f"🎯 Average confidence: {avg_confidence:.1f}%", "SUCCESS")
    
    def print_signal_table_header(self):
        """Print the signal table header"""
        headers = ["Time", "Pair", "Signal", "Confidence", "Pattern", "Filters", "Score"]
        widths = [8, 10, 8, 12, 12, 15, 8]
        colors = ["HEADER"] * len(headers)
        
        # Print separator
        print_colored("=" * 85, "HEADER")
        
        # Print headers
        row = ""
        for header, width, color in zip(headers, widths, colors):
            color_code = DISPLAY_CONFIG["COLORS"][color]
            reset_code = DISPLAY_CONFIG["COLORS"]["RESET"]
            row += f"{color_code}{header:<{width}}{reset_code}"
        print(row)
        
        print_colored("=" * 85, "HEADER")
    
    def print_signal_row(self, signal):
        """Print a single signal row"""
        time_str = signal.get('time', 'N/A')
        pair = signal.get('pair', 'N/A')
        signal_type = signal.get('signal', 'N/A')
        confidence = f"{signal.get('confidence_score', 0):.1f}%"
        pattern = signal.get('pattern_type', 'N/A')[:11]
        filters_passed = f"{signal.get('filters_passed', 0)}/5"
        score = f"{signal.get('total_score', 0):.0f}"
        
        # Determine colors
        signal_color = "BUY" if signal_type == "BUY" else "SELL" if signal_type == "SELL" else "INFO"
        confidence_color = "SUCCESS" if signal.get('confidence_score', 0) >= 80 else "WARNING" if signal.get('confidence_score', 0) >= 60 else "ERROR"
        
        values = [time_str, pair, signal_type, confidence, pattern, filters_passed, score]
        widths = [8, 10, 8, 12, 12, 15, 8]
        colors = ["INFO", "INFO", signal_color, confidence_color, "INFO", "INFO", "SUCCESS"]
        
        row = ""
        for value, width, color in zip(values, widths, colors):
            color_code = DISPLAY_CONFIG["COLORS"][color]
            reset_code = DISPLAY_CONFIG["COLORS"]["RESET"]
            row += f"{color_code}{str(value):<{width}}{reset_code}"
        print(row)

def main():
    """Main function for advanced signal generator"""
    try:
        generator = AdvancedSignalGenerator()
        
        # Get user inputs
        params = generator.get_user_inputs()
        
        # Generate signals
        print_colored("🚀 Starting advanced signal generation...", "INFO", bold=True)
        print()
        
        signals = generator.generate_signals(params)
        
        # Display results
        generator.display_signals(signals)
        
        # Clean up temporary files
        print()
        print_colored("🧹 Cleaning up temporary files...", "INFO")
        # Cleanup logic will be implemented in pattern_analyzer
        
        print_colored("✅ Advanced signal generation completed!", "SUCCESS", bold=True)
        
    except KeyboardInterrupt:
        print_colored("\n⚠️ Signal generation interrupted by user", "WARNING")
    except Exception as e:
        print_colored(f"❌ Error in signal generation: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
