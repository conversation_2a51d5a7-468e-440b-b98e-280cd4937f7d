#!/usr/bin/env python3
"""
Pattern Analyzer for Historical Time-Based Pattern Recognition
Analyzes candle patterns at specific times across multiple days
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
from collections import defaultdict
from utils import print_colored
from config import ADVANCED_SIGNAL_CONFIG

class PatternAnalyzer:
    def __init__(self):
        """Initialize the pattern analyzer"""
        self.patterns = {}
        self.temp_files = []
    
    def find_time_patterns(self, df, analysis_days, start_time, end_time, timeframe):
        """Find patterns by analyzing every single candle within the time range across multiple days"""
        print_colored(f"🔍 Analyzing {analysis_days} days of patterns...", "INFO")

        # Parse time strings
        start_hour, start_minute = map(int, start_time.split(':'))
        end_hour, end_minute = map(int, end_time.split(':'))

        # Group data by date
        df['date'] = pd.to_datetime(df['time']).dt.date
        df['hour'] = pd.to_datetime(df['time']).dt.hour
        df['minute'] = pd.to_datetime(df['time']).dt.minute

        # Get only active trading days (days with actual candles)
        all_dates = sorted(df['date'].unique())
        active_trading_days = []

        # Filter out days with very few candles (holidays/weekends)
        for date in all_dates:
            day_candles = len(df[df['date'] == date])
            if day_candles > 50:  # At least 50 candles = active trading day
                active_trading_days.append(date)

        # Get the last N active trading days
        if len(active_trading_days) >= analysis_days:
            selected_dates = active_trading_days[-analysis_days:]
        else:
            selected_dates = active_trading_days

        print_colored(f"📅 Found {len(all_dates)} total dates, {len(active_trading_days)} active trading days", "INFO")
        print_colored(f"📊 Selected last {len(selected_dates)} active trading days for analysis", "INFO")

        if len(selected_dates) < 2:
            print_colored(f"❌ Need at least 2 active trading days, found {len(selected_dates)}", "ERROR")
            return []

        # Collect all candles within time range for each day
        all_candles_by_time = defaultdict(list)
        total_candles_analyzed = 0

        for date in selected_dates:
            day_data = df[df['date'] == date].copy()

            # Filter by time window
            time_filtered = day_data[
                ((day_data['hour'] > start_hour) |
                 ((day_data['hour'] == start_hour) & (day_data['minute'] >= start_minute))) &
                ((day_data['hour'] < end_hour) |
                 ((day_data['hour'] == end_hour) & (day_data['minute'] <= end_minute)))
            ]

            print_colored(f"📊 Day {date}: Found {len(time_filtered)} candles in time range", "INFO")
            total_candles_analyzed += len(time_filtered)

            # Analyze each candle in the time range
            for _, candle in time_filtered.iterrows():
                time_key = f"{candle['hour']:02d}:{candle['minute']:02d}"

                # Determine candle direction and strength
                candle_info = self.analyze_candle(candle)
                all_candles_by_time[time_key].append({
                    'date': date,
                    'direction': candle_info['direction'],
                    'strength': candle_info['strength'],
                    'body_ratio': candle_info['body_ratio'],
                    'price': candle['close'],
                    'time_key': time_key
                })

        print_colored(f"🕐 Total candles analyzed: {total_candles_analyzed}", "INFO")
        print_colored(f"🕐 Unique time slots found: {len(all_candles_by_time)}", "INFO")

        # Find consistent patterns for each time slot
        consistent_patterns = self.find_consistent_patterns(all_candles_by_time, len(selected_dates))

        return consistent_patterns
    
    def analyze_candle(self, candle):
        """Analyze individual candle characteristics - simplified to only check direction"""
        open_price = candle['open']
        close_price = candle['close']
        high_price = candle['high']
        low_price = candle['low']

        # Calculate basic candle metrics (for strength filter only)
        body_size = abs(close_price - open_price)
        candle_range = high_price - low_price
        body_ratio = body_size / candle_range if candle_range > 0 else 0

        # Determine direction (main focus)
        if close_price > open_price:
            direction = 'BULLISH'
        elif close_price < open_price:
            direction = 'BEARISH'
        else:
            direction = 'DOJI'

        # Simple strength check (no volume analysis)
        strength_threshold = ADVANCED_SIGNAL_CONFIG['CANDLE_STRENGTH_THRESHOLD']
        strength = 'STRONG' if body_ratio >= strength_threshold else 'WEAK'

        return {
            'direction': direction,
            'strength': strength,
            'body_ratio': body_ratio
        }
    
    def find_consistent_patterns(self, time_patterns, analysis_days):
        """Find time slots with consistent patterns across all days"""
        consistent_patterns = []
        min_confidence = ADVANCED_SIGNAL_CONFIG['MIN_PATTERN_CONFIDENCE']

        print_colored(f"🔍 Analyzing {len(time_patterns)} time slots for patterns...", "INFO")
        print_colored(f"📊 Minimum confidence required: {min_confidence:.1%}", "INFO")

        # Debug: Show some sample time slots
        sample_count = 0
        for time_slot, candles in time_patterns.items():
            if len(candles) < analysis_days:
                continue  # Not enough data for this time slot

            # Count directions
            bullish_count = sum(1 for c in candles if c['direction'] == 'BULLISH')
            bearish_count = sum(1 for c in candles if c['direction'] == 'BEARISH')
            doji_count = sum(1 for c in candles if c['direction'] == 'DOJI')

            # Count strong candles
            strong_count = sum(1 for c in candles if c['strength'] == 'STRONG')

            # Calculate consistency
            total_candles = len(candles)
            bullish_ratio = bullish_count / total_candles
            bearish_ratio = bearish_count / total_candles
            strong_ratio = strong_count / total_candles

            # Debug: Show first few time slots
            if sample_count < 5:
                print_colored(f"  {time_slot}: {bullish_count}B/{bearish_count}S/{doji_count}D (B:{bullish_ratio:.1%}, S:{bearish_ratio:.1%})", "INFO")
                sample_count += 1

            # Determine if pattern is consistent enough
            pattern_signal = None
            confidence = 0

            if bullish_ratio >= min_confidence:
                pattern_signal = 'BUY'
                confidence = bullish_ratio
            elif bearish_ratio >= min_confidence:
                pattern_signal = 'SELL'
                confidence = bearish_ratio

            if pattern_signal:
                # Calculate additional metrics
                avg_body_ratio = sum(c['body_ratio'] for c in candles) / total_candles
                avg_price = sum(c['price'] for c in candles) / total_candles

                pattern_info = {
                    'time': time_slot,
                    'signal': pattern_signal,
                    'confidence': confidence,
                    'pattern_type': f"{pattern_signal.lower()}_consistent",
                    'total_occurrences': total_candles,
                    'signal_occurrences': bullish_count if pattern_signal == 'BUY' else bearish_count,
                    'strong_candles': strong_count,
                    'strong_ratio': strong_ratio,
                    'avg_body_ratio': avg_body_ratio,
                    'avg_price': avg_price,
                    'analysis_days': analysis_days,
                    'raw_data': candles
                }

                consistent_patterns.append(pattern_info)
                print_colored(f"✅ Found pattern at {time_slot}: {pattern_signal} ({confidence:.1%})", "SUCCESS")

        # Sort by confidence
        consistent_patterns.sort(key=lambda x: x['confidence'], reverse=True)

        print_colored(f"✅ Found {len(consistent_patterns)} consistent patterns", "SUCCESS")

        return consistent_patterns
    
    def calculate_pattern_strength(self, pattern):
        """Calculate overall pattern strength score"""
        base_score = pattern['confidence'] * 100
        
        # Bonus for strong candles
        strong_bonus = pattern['strong_ratio'] * 20
        
        # Bonus for high body ratio
        body_bonus = pattern['avg_body_ratio'] * 15
        
        # Bonus for multiple occurrences
        occurrence_bonus = min(pattern['total_occurrences'] * 2, 10)
        
        total_score = base_score + strong_bonus + body_bonus + occurrence_bonus
        
        return min(total_score, 100)  # Cap at 100
    
    def get_pattern_description(self, pattern):
        """Get human-readable pattern description"""
        signal = pattern['signal']
        time_slot = pattern['time']
        confidence = pattern['confidence'] * 100
        occurrences = pattern['signal_occurrences']
        total = pattern['total_occurrences']
        
        description = f"{signal} pattern at {time_slot}: {occurrences}/{total} days ({confidence:.1f}%)"
        
        if pattern['strong_ratio'] > 0.5:
            description += " - Strong candles"
        
        return description
    
    def export_pattern_details(self, patterns, filename=None):
        """Export detailed pattern analysis to CSV"""
        if not patterns:
            return None
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pattern_analysis_{timestamp}.csv"
        
        # Prepare data for export
        export_data = []
        for pattern in patterns:
            for candle_data in pattern['raw_data']:
                export_data.append({
                    'time_slot': pattern['time'],
                    'signal': pattern['signal'],
                    'pattern_confidence': pattern['confidence'],
                    'date': candle_data['date'],
                    'direction': candle_data['direction'],
                    'strength': candle_data['strength'],
                    'body_ratio': candle_data['body_ratio'],
                    'price': candle_data['price']
                })
        
        # Create DataFrame and save
        df = pd.DataFrame(export_data)
        df.to_csv(filename, index=False)
        
        self.temp_files.append(filename)
        print_colored(f"📊 Pattern details exported to {filename}", "INFO")
        
        return filename
    
    def cleanup_temp_files(self):
        """Clean up temporary files created during analysis"""
        import os
        
        for file_path in self.temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print_colored(f"🗑️ Removed {file_path}", "INFO")
            except Exception as e:
                print_colored(f"⚠️ Could not remove {file_path}: {str(e)}", "WARNING")
        
        self.temp_files.clear()
    
    def get_next_signal_time(self, patterns, current_time=None):
        """Get the next upcoming signal time based on patterns"""
        if not patterns:
            return None
        
        if current_time is None:
            current_time = datetime.now().time()
        
        # Convert pattern times to time objects and sort
        pattern_times = []
        for pattern in patterns:
            hour, minute = map(int, pattern['time'].split(':'))
            pattern_time = time(hour, minute)
            pattern_times.append((pattern_time, pattern))
        
        pattern_times.sort(key=lambda x: x[0])
        
        # Find next signal time
        for pattern_time, pattern in pattern_times:
            if pattern_time > current_time:
                return {
                    'time': pattern['time'],
                    'signal': pattern['signal'],
                    'confidence': pattern['confidence'],
                    'minutes_until': self.calculate_minutes_until(current_time, pattern_time)
                }
        
        # If no time today, return first time tomorrow
        if pattern_times:
            first_pattern = pattern_times[0][1]
            return {
                'time': first_pattern['time'],
                'signal': first_pattern['signal'],
                'confidence': first_pattern['confidence'],
                'minutes_until': self.calculate_minutes_until(current_time, pattern_times[0][0]) + 1440  # +24 hours
            }
        
        return None
    
    def calculate_minutes_until(self, current_time, target_time):
        """Calculate minutes until target time"""
        current_minutes = current_time.hour * 60 + current_time.minute
        target_minutes = target_time.hour * 60 + target_time.minute
        
        if target_minutes > current_minutes:
            return target_minutes - current_minutes
        else:
            return (24 * 60) - current_minutes + target_minutes  # Next day
